<template>
  <ContainerQuery>
    <div slot="left">
      <el-button type="primary" @click="refreshData">刷新</el-button>
    </div>
    <el-form slot="more" size="small" :inline="true">
      <el-form-item>
        <SelectSupplier
          v-model="searchParams.supplierId"
          style="width: 320px"
          placeholder="请选择供应商"
          @clear="clearSupplier"
          @change="selUnitSupplier"
          @keyup.enter.native="handleQuery"
        >
          <el-button slot="append" icon="el-icon-search" @click="handleQuery"></el-button>
        </SelectSupplier>
      </el-form-item>
      <el-form-item>
        <el-select
          v-model="searchParams.status"
          clearable
          placeholder="请选择代销状态"
          style="width: 220px"
          @change="pageChange(1)"
        >
          <el-option label="全部" value=""></el-option>
          <el-option label="已启用" value="1"></el-option>
          <el-option label="已禁用" value="0"></el-option>
        </el-select>
      </el-form-item>
    </el-form>

    <el-table v-loading="loading" :data="tableData" style="width: 100%">
      <el-table-column prop="supplierCode" label="供应商编码" min-width="120"></el-table-column>
      <el-table-column prop="supplierName" label="供应商名称" min-width="180"></el-table-column>
      <el-table-column prop="contactName" label="联系人" min-width="120"></el-table-column>
      <el-table-column prop="contactPhone" label="联系电话" min-width="120"></el-table-column>

      <el-table-column prop="settlementCycle" label="结算周期" min-width="120"></el-table-column>
      <el-table-column prop="depositAmount" label="保证金余额" min-width="120">
        <template slot-scope="scope">
          {{ scope.row.depositAmount | formatMoney }}
        </template>
      </el-table-column>
      <el-table-column prop="status" label="代销状态" min-width="100">
        <template slot-scope="scope">
          <el-switch
            v-model="scope.row.status"
            class="switchStyle"
            active-color="#36B365"
            :active-value="1"
            :inactive-value="0"
            active-text="启用"
            inactive-text="禁用"
            @change="toggleStatus(scope.row)"
          ></el-switch>
        </template>
      </el-table-column>
      <el-table-column label="操作" width="160" fixed="right">
        <template slot-scope="scope">
          <div style="height: 34px; line-height: 34px">
            <el-button type="text" @click.stop="editConfig(scope.row)">编辑配置</el-button>
            <el-button type="text" @click.stop="depositManage(scope.row)">保证金管理</el-button>
          </div>
        </template>
      </el-table-column>
    </el-table>

    <FooterPage
      :page-size="pageSize"
      :total-page.sync="total"
      :current-page.sync="page"
      @pageChange="pageChange"
      @sizeChange="sizeChange"
    ></FooterPage>

    <!-- 编辑配置抽屉 -->
    <el-drawer
      title="编辑代销配置"
      :visible.sync="configDrawer"
      direction="rtl"
      size="40%"
      :before-close="closeConfigDrawer"
      :destroy-on-close="true"
      :wrapper-closable="false"
      :modal-append-to-body="false"
      append-to-body
    >
      <div style="padding: 20px">
        <ConsignmentConfig
          v-if="configDrawer"
          ref="configForm"
          v-model="currentConfig"
          :supplier-id="currentSupplierId"
          @enabledChange="configEnabledChange"
        />
        <div style="text-align: center; margin-top: 20px">
          <el-button @click="closeConfigDrawer">取消</el-button>
          <el-button type="primary" @click="saveConfig">保存</el-button>
        </div>
      </div>
    </el-drawer>

    <!-- 保证金管理抽屉 -->
    <el-drawer
      title="保证金管理"
      :visible.sync="depositDrawer"
      direction="rtl"
      size="40%"
      :before-close="closeDepositDrawer"
      :destroy-on-close="true"
      :wrapper-closable="false"
      :modal-append-to-body="false"
      append-to-body
    >
      <div style="padding: 20px">
        <el-form ref="depositForm" :model="depositForm" label-width="100px" size="small">
          <el-form-item label="供应商：">
            <span>{{ currentSupplierName }}</span>
          </el-form-item>
          <el-form-item label="当前余额：">
            <span>{{ currentDepositAmount | formatMoney }}</span>
          </el-form-item>
          <el-form-item
            label="充值金额："
            prop="amount"
            :rules="[
              { required: true, message: '请输入充值金额', trigger: 'blur' },
              { type: 'number', min: 0.01, message: '金额必须大于0', trigger: 'blur' },
            ]"
          >
            <el-input-number v-model="depositForm.amount" :min="0.01" :precision="2" :step="100"></el-input-number>
          </el-form-item>
          <el-form-item label="备注：">
            <el-input v-model="depositForm.remark" type="textarea" placeholder="请输入备注信息"></el-input>
          </el-form-item>
        </el-form>
        <div style="text-align: center; margin-top: 20px">
          <el-button @click="closeDepositDrawer">取消</el-button>
          <el-button type="primary" @click="saveDeposit">确认充值</el-button>
        </div>
      </div>
    </el-drawer>
  </ContainerQuery>
</template>

<script>
import {
    depositFunds,
    getConsignmentConfig,
    getConsignmentSuppliers,
    updateConsignmentConfig,
} from "@/api/SupplierConsignment";
import FooterPage from "@/component/common/FooterPage";
import SelectSupplier from "@/component/common/SelectSupplier";
import ConsignmentConfig from "@/component/supplier/ConsignmentConfig";

export default {
  name: "ConsignmentManage",
  components: {
    FooterPage,
    SelectSupplier,
    ConsignmentConfig,
  },
  filters: {
    formatMoney(value) {
      if (!value) return "¥0.00";
      return "¥" + parseFloat(value).toFixed(2);
    },
  },
  data() {
    return {
      loading: false,
      tableData: [],
      total: 0,
      page: 1,
      pageSize: 10,
      searchParams: {
        supplierId: "",
        status: "",
      },

      // 配置抽屉
      configDrawer: false,
      currentSupplierId: null,
      currentConfig: {},

      // 保证金抽屉
      depositDrawer: false,
      currentSupplierName: "",
      currentDepositAmount: 0,
      depositForm: {
        amount: 100,
        remark: "",
      },
    };
  },
  created() {
    this.getData();
  },
  methods: {
    async getData() {
      this.loading = true;
      try {
        const res = await getConsignmentSuppliers({
          page: this.page,
          pageSize: this.pageSize,
          supplierId: this.searchParams.supplierId,
          status: this.searchParams.status,
        });

        console.log('API响应完整数据:', res);

        if (res.errorcode === 0) {
          // 打印完整的后端返回数据，包括结构
          console.log('后端返回的供应商数据:', JSON.stringify(res.data, null, 2));

          // 处理返回的数据，将后端字段映射到前端需要的字段
          this.tableData = res.data.map(item => {
            console.log('处理单个供应商数据:', item);

            // 解析consignmentConfig
            let config = {};
            if (item.consignmentConfig) {
              try {
                config = typeof item.consignmentConfig === 'string'
                  ? JSON.parse(item.consignmentConfig)
                  : item.consignmentConfig;
              } catch (e) {
                console.error('解析consignmentConfig失败:', e);
              }
            }

            // 确保同时有id和supplierId字段，便于后续操作
            const mappedItem = {
              // 使用原始数据中的id字段
              id: parseInt(item.id) || 0,
              supplierCode: item.code || '',
              supplierName: item.title || '',
              // 确保有supplierId字段，与id保持一致
              supplierId: parseInt(item.id) || 0,
              contactName: item.realName || '',
              contactPhone: item.mobile || '',
              settlementCycle: '月结',  // 默认值，可根据实际情况修改
              depositAmount: parseFloat(item.depositAccount) || 0,
              status: config.enabled ? 1 : 0
            };

            console.log('映射后的供应商数据项:', mappedItem);
            return mappedItem;
          });
          this.total = res.pageTotal || 0;
        } else {
          this.$message.error(res.data || "获取供应商代销列表失败");
        }
      } catch (error) {
        this.$message.error("获取供应商代销列表失败");
        console.error(error);
      } finally {
        this.loading = false;
      }
    },

    refreshData() {
      this.getData();
    },

    // 查询按钮
    handleQuery() {
      this.pageChange(1);
    },

    pageChange(val) {
      this.page = val;
      this.getData();
    },

    sizeChange(val) {
      this.pageSize = val;
      this.page = 1;
      this.getData();
    },

    clearSupplier() {
      this.searchParams.supplierId = "";
      this.pageChange(1);
    },

    selUnitSupplier() {
      this.pageChange(1);
    },

    // 编辑配置
    async editConfig(row) {
      console.log('编辑配置行数据:', row);

      // 检查行数据是否完整
      if (!row) {
        this.$message.error("供应商数据不完整");
        return;
      }

      // 从行数据中获取供应商ID
      // 根据您提供的截图，我们需要从supplierCode中提取ID
      let supplierId = 0;

      // 尝试从supplierCode中提取ID
      if (row.supplierCode) {
        const match = row.supplierCode.match(/SUPPLIER0+(\d+)/);
        if (match && match[1]) {
          supplierId = parseInt(match[1]);
          console.log('从supplierCode提取的ID:', supplierId);
        }
      }

      // 如果上面的方法无法获取ID，尝试其他方法
      if (!supplierId && (row.supplierId || row.id)) {
        supplierId = parseInt(row.supplierId || row.id);
        console.log('从supplierId或id字段获取的ID:', supplierId);
      }

      if (!supplierId) {
        this.$message.error("无法获取供应商ID");
        console.error("无法从行数据中获取供应商ID:", row);
        return;
      }

      console.log('最终使用的供应商ID:', supplierId);
      this.currentSupplierId = supplierId;

      try {
        // 先显示抽屉，避免数据加载延迟
        this.currentConfig = {
          enabled: row.status === 1,
          depositRequired: false,
          minDepositAmount: 1000,
        };

        this.configDrawer = true;

        // 然后异步加载完整配置
        console.log('请求配置数据，供应商ID:', supplierId);
        const res = await getConsignmentConfig(supplierId);
        console.log('获取配置响应:', res);

        if (res.errorcode === 0 && res.data && this.configDrawer) {
          // 只有在抽屉仍然打开的情况下更新数据
          const data = res.data;
          console.log('获取到的配置数据:', data);

          this.currentConfig = {
            enabled: data.enabled !== undefined ? data.enabled : row.status === 1,
            depositRequired: data.depositRequired || false,
            minDepositAmount: data.minDepositAmount || 1000,
          };
        }
      } catch (error) {
        console.error("获取配置失败：", error);
        // 错误时不关闭抽屉，只显示错误信息
        this.$message.error("获取完整配置失败，显示默认配置：" + (error.message || "未知错误"));
      }
    },

    configEnabledChange(val) {
      this.currentConfig.enabled = val;
    },

    closeConfigDrawer() {
      this.configDrawer = false;
      this.currentSupplierId = null;
      this.currentConfig = {};
    },

    async saveConfig() {
      if (!this.currentSupplierId) {
        this.$message.error("供应商ID不能为空");
        return;
      }

      try {
        // 创建一个新对象，避免引用问题
        const configToSave = {
          enabled: this.currentConfig.enabled,
          depositRequired: this.currentConfig.depositRequired,
          minDepositAmount: this.currentConfig.minDepositAmount,
        };

        await updateConsignmentConfig(this.currentSupplierId, configToSave);
        this.$message.success("保存成功");
        this.closeConfigDrawer();
        this.getData();
      } catch (error) {
        console.error("保存配置失败：", error);
        this.$message.error("保存失败：" + (error.message || "未知错误"));
      }
    },

    // 切换状态
    async toggleStatus(row) {
      console.log('切换状态行数据:', row);

      // 检查行数据是否完整
      if (!row) {
        this.$message.error("供应商数据不完整");
        return;
      }

      // 从行数据中获取供应商ID
      let supplierId = 0;

      // 尝试从supplierCode中提取ID
      if (row.supplierCode) {
        const match = row.supplierCode.match(/SUPPLIER0+(\d+)/);
        if (match && match[1]) {
          supplierId = parseInt(match[1]);
          console.log('从supplierCode提取的ID:', supplierId);
        }
      }

      // 如果上面的方法无法获取ID，尝试其他方法
      if (!supplierId && (row.supplierId || row.id)) {
        supplierId = parseInt(row.supplierId || row.id);
        console.log('从supplierId或id字段获取的ID:', supplierId);
      }

      if (!supplierId) {
        this.$message.error("无法获取供应商ID");
        console.error("无法从行数据中获取供应商ID:", row);
        return;
      }

      console.log('最终使用的供应商ID:', supplierId);

      try {
        console.log('发送状态更新请求，供应商ID:', supplierId, '状态:', row.status === 1);
        const res = await updateConsignmentConfig(supplierId, {
          enabled: row.status === 1,
        });
        console.log('状态更新响应:', res);

        if (res.errorcode === 0) {
          this.$message.success(row.status === 1 ? "启用成功" : "禁用成功");
        } else {
          // 操作失败时恢复原状态
          row.status = row.status === 1 ? 0 : 1;
          this.$message.error((row.status === 0 ? "启用" : "禁用") + "失败：" + (res.data || "未知错误"));
        }
      } catch (error) {
        // 操作失败时恢复原状态
        row.status = row.status === 1 ? 0 : 1;
        this.$message.error((row.status === 0 ? "启用" : "禁用") + "失败：" + (error.message || "未知错误"));
      }
    },

    // 保证金管理
    depositManage(row) {
      console.log('保证金管理行数据:', row);

      // 检查行数据是否完整
      if (!row) {
        this.$message.error("供应商数据不完整");
        return;
      }

      // 从行数据中获取供应商ID
      let supplierId = 0;

      // 尝试从supplierCode中提取ID
      if (row.supplierCode) {
        const match = row.supplierCode.match(/SUPPLIER0+(\d+)/);
        if (match && match[1]) {
          supplierId = parseInt(match[1]);
          console.log('从supplierCode提取的ID:', supplierId);
        }
      }

      // 如果上面的方法无法获取ID，尝试其他方法
      if (!supplierId && (row.supplierId || row.id)) {
        supplierId = parseInt(row.supplierId || row.id);
        console.log('从supplierId或id字段获取的ID:', supplierId);
      }

      if (!supplierId) {
        this.$message.error("无法获取供应商ID");
        console.error("无法从行数据中获取供应商ID:", row);
        return;
      }

      console.log('最终使用的供应商ID:', supplierId);

      this.currentSupplierId = supplierId;
      this.currentSupplierName = row.supplierName || row.title || '';
      this.currentDepositAmount = row.depositAmount || row.depositAccount || 0;
      this.depositForm = {
        amount: 100,
        remark: "",
      };

      // 确保在数据设置完成后再显示抽屉
      this.$nextTick(() => {
        this.depositDrawer = true;
      });
    },

    closeDepositDrawer() {
      this.depositDrawer = false;
    },

    async saveDeposit() {
      this.$refs.depositForm.validate(async (valid) => {
        if (valid) {
          try {
            if (!this.currentSupplierId) {
              this.$message.error("供应商ID不能为空");
              return;
            }

            console.log('发送充值请求，供应商ID:', this.currentSupplierId, '金额:', this.depositForm.amount);
            const res = await depositFunds(this.currentSupplierId, this.depositForm.amount, this.depositForm.remark);
            console.log('充值响应:', res);

            if (res.errorcode === 0) {
              this.$message.success("充值成功");
              this.closeDepositDrawer();
              this.getData();
            } else {
              this.$message.error("充值失败：" + (res.data || "未知错误"));
            }
          } catch (error) {
            console.error("充值异常:", error);
            this.$message.error("充值失败：" + (error.message || "未知错误"));
          }
        }
      });
    },
  },
};
</script>

<style scoped>
.el-drawer__body {
  overflow: auto;
  padding: 0;
}
.el-table .cell .el-button {
  padding: 0 8px;
  margin: 0 5px;
}
.el-table .cell .el-button:first-child {
  margin-left: 0;
}
</style>

<style>
/* 全局样式修复 */
.el-drawer__container {
  position: fixed;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  overflow: hidden;
  margin: 0;
}
</style>
